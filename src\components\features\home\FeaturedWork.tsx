'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Play } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { VideoDialog } from '@/components/ui/video-dialog';
import { getFeaturedVideos, getYouTubeThumbnail, type Video } from '@/lib/api';
import { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';

export default function FeaturedWork() {
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const containerRef = useStaggeredScrollAnimation();

  useEffect(() => {
    const fetchVideos = async () => {
      try {
        const featuredVideos = await getFeaturedVideos();
        // Show only first 6 videos for featured section
        setVideos(featuredVideos.slice(0, 6));
      } catch (error) {
        console.error('Error fetching featured videos:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, []);



  return (
    <section id="featured-work" className="section-padding bg-background" ref={containerRef}>
      <div className="container mx-auto container-padding">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="heading-secondary mb-4">
            Featured Work
          </h2>
          <p className="text-body-large max-w-2xl mx-auto">
            A selection of projects showcasing diverse editing styles and creative storytelling techniques.
          </p>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-card rounded-xl overflow-hidden shadow-lg">
                <div className="aspect-video bg-gray-200 animate-pulse"></div>
                <div className="p-6">
                  <div className="h-6 bg-gray-200 rounded animate-pulse mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : videos.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground">No featured videos available at the moment.</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {videos.map((video) => (
                <div key={video._id}>
                  <div
                    onClick={() => setSelectedVideo(video.id)}
                    className="group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer h-[420px] flex flex-col"
                  >
                    <div className="relative aspect-video overflow-hidden flex-shrink-0">
                      <Image
                        src={getYouTubeThumbnail(video.id)}
                        alt={video.title}
                        fill
                        className="object-cover transition-transform duration-500 group-hover:scale-110"
                      />

                      <div className="absolute inset-0 bg-primary/20 group-hover:bg-primary/40 transition-colors duration-300 flex items-center justify-center">
                        <div className="bg-card/90 backdrop-blur-sm rounded-full p-4 group-hover:scale-110 transition-transform duration-300">
                          <Play className="h-8 w-8 text-primary ml-1" fill="currentColor" />
                        </div>
                      </div>

                       {/* Category Badge */}
                    {video.category && (
                      <div className="absolute top-4 left-4">
                        <Badge variant="secondary" className="bg-accent/90 text-accent-foreground">
                          {video.category}
                        </Badge>
                      </div>
                    )}
                  </div>


                    <div className="p-6 flex-1 flex flex-col">
                      <h3 className="text-xl font-heading font-semibold text-primary mb-3 group-hover:text-secondary transition-colors duration-300 line-clamp-2">
                        {video.title}
                      </h3>

                      {video.description && (
                        <p className="text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-2 flex-1">
                          {video.description}
                        </p>
                      )}

                      {video.tags && video.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-auto">
                          {video.tags.slice(0, 3).map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {video.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{video.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Video Dialog */}
            <VideoDialog
              isOpen={selectedVideo !== null}
              onClose={() => setSelectedVideo(null)}
              videoId={selectedVideo}
              videoTitle={videos.find(v => v.id === selectedVideo)?.title}
              variant="default"
            />
          </>
        )}

        {/* View More Button */}
        <div className="text-center mt-12">
          <Button asChild className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg">
            <Link href="/videos">
              View All Videos
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
