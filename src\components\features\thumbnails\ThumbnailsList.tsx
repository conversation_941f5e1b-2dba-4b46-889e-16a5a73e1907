'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { getFeaturedThumbnails, type Thumbnail } from '@/lib/api';
import { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';

export default function ThumbnailsList() {
  const [thumbnails, setThumbnails] = useState<Thumbnail[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoScrollPaused, setIsAutoScrollPaused] = useState(false);
  const containerRef = useStaggeredScrollAnimation();
  const carouselRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchThumbnails = async () => {
      try {
        const featuredThumbnails = await getFeaturedThumbnails();
        console.log('Featured thumbnails:', featuredThumbnails);
        // Show only first 12 thumbnails for featured section
        setThumbnails(featuredThumbnails.slice(0, 12));
      } catch (error) {
        console.error('Error fetching featured thumbnails:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchThumbnails();
  }, []);

  // Carousel navigation functions with loop
  const nextSlide = () => {
    setCurrentIndex(prev => (prev >= thumbnails.length - 1 ? 0 : prev + 1));
  };

  const prevSlide = () => {
    setCurrentIndex(prev => (prev <= 0 ? thumbnails.length - 1 : prev - 1));
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  // Auto-scroll effect
  useEffect(() => {
    if (thumbnails.length <= 1 || isAutoScrollPaused) return;

    const interval = setInterval(() => {
      setCurrentIndex(prev => {
        const max = thumbnails.length - 1;
        return prev >= max ? 0 : prev + 1;
      });
    }, 4000); // Auto-scroll every 4 seconds

    return () => clearInterval(interval);
  }, [thumbnails.length, isAutoScrollPaused]);

  // Handle current index when thumbnails change
  useEffect(() => {
    const newMaxIndex = Math.max(0, thumbnails.length - 1);
    if (currentIndex > newMaxIndex) {
      setCurrentIndex(newMaxIndex);
    }
  }, [currentIndex, thumbnails.length]);

  return (
    <section id="thumbnails" className="py-20 bg-muted/30" ref={containerRef}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-foreground mb-4">
            YouTube Thumbnails
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto font-body">
            Creative thumbnail designs that capture attention and drive engagement across various video content.
          </p>
        </div>

        {loading ? (
          <div className="relative max-w-4xl mx-auto">
            <div className="aspect-video bg-gray-200 animate-pulse rounded-xl shadow-lg"></div>
          </div>
        ) : thumbnails.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground font-body">No thumbnails available at the moment.</p>
          </div>
        ) : (
          <>
            {/* Carousel Container */}
            <div className="relative max-w-4xl mx-auto">
              {/* Navigation Arrows */}
              {thumbnails.length > 1 && (
                <>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute -left-4 top-1/2 -translate-y-1/2 z-10 bg-background/80 hover:bg-background shadow-lg rounded-full h-12 w-12"
                    onClick={prevSlide}
                  >
                    <ChevronLeft size={24} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute -right-4 top-1/2 -translate-y-1/2 z-10 bg-background/80 hover:bg-background shadow-lg rounded-full h-12 w-12"
                    onClick={nextSlide}
                  >
                    <ChevronRight size={24} />
                  </Button>
                </>
              )}

              {/* Carousel Track */}
              <div
                className="overflow-hidden"
                ref={carouselRef}
                onMouseEnter={() => setIsAutoScrollPaused(true)}
                onMouseLeave={() => setIsAutoScrollPaused(false)}
              >
                <div
                  className="flex transition-transform duration-500 ease-in-out"
                  style={{
                    transform: `translateX(-${currentIndex * 100}%)`
                  }}
                >
                  {thumbnails.map((thumbnail) => (
                    <div
                      key={thumbnail._id}
                      className="flex-shrink-0 w-full"
                    >
                      <div className="group relative w-full max-w-4xl mx-auto overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500">
                        <div className="relative aspect-video">
                          <Image
                            src={thumbnail.thumbnailUrl}
                            alt={thumbnail.title}
                            fill
                            className="object-cover transition-transform duration-500 group-hover:scale-105"
                          />

                          {/* Category Badge */}
                          {thumbnail.category && (
                            <div className="absolute top-4 left-4">
                              <Badge variant="secondary" className="bg-accent/90 text-accent-foreground text-sm font-accent">
                                {thumbnail.category}
                              </Badge>
                            </div>
                          )}

                          {/* Title Overlay (appears on hover) */}
                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <h3 className="text-white font-heading font-semibold text-xl line-clamp-2">
                              {thumbnail.title}
                            </h3>
                            {thumbnail.description && (
                              <p className="text-white/80 text-sm mt-2 line-clamp-2 font-body">
                                {thumbnail.description}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Carousel Dots */}
              {thumbnails.length > 1 && (
                <div className="flex justify-center mt-8 gap-2">
                  {Array.from({ length: thumbnails.length }).map((_, index) => (
                    <button
                      key={index}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        index === currentIndex
                          ? 'bg-accent scale-125'
                          : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                      }`}
                      onClick={() => goToSlide(index)}
                      aria-label={`Go to slide ${index + 1}`}
                    />
                  ))}
                </div>
              )}
            </div>

          </>
        )}
      </div>
    </section>
  );
}
