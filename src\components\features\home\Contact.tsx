'use client';

import { useState } from 'react';
import { Send, Mail, Phone, MapPin, Linkedin, Instagram, Youtube, Facebook } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const containerRef = useStaggeredScrollAnimation();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setFormData({ name: '', email: '', subject: '', message: '' });
    } catch {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus('idle'), 5000);
    }
  };

  const contactInfo = [
    {
      icon: Mail,
      label: 'Email',
      value: '<EMAIL>',
      href: 'mailto:<EMAIL>'
    },
    {
      icon: Phone,
      label: 'Phone',
      value: '+977 ************',
      href: 'tel:+9779840692118'
    },
    {
      icon: MapPin,
      label: 'Location',
      value: 'Kathmandu, Nepal',
      href: '#'
    }
  ];

  const socialLinks = [
    { icon: Linkedin, href: 'https://www.linkedin.com/in/uttamrimal', label: 'LinkedIn' },
    { icon: Instagram, href: 'https://www.instagram.com/uttamrimal', label: 'Instagram' },
    { icon: Youtube, href: 'https://www.youtube.com/c/UttamRimal', label: 'YouTube' },
    { icon: Facebook, href: 'https://www.facebook.com/uttamrimal', label: 'Facebook' },
  ];

  return (
    <section id="contact" className="py-16 bg-background" ref={containerRef}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-primary mb-4 animate-slide-up">
            Let&apos;s Create Together
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto animate-fade-in stagger-2">
            Have a project in mind? Let&apos;s discuss how we can bring your vision to life through compelling video content.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Contact Form */}
          <Card className="shadow-lg animate-on-scroll animate-slide-in-left stagger-3 hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-2xl font-heading text-primary">
                Send a Message
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-5">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Your Name *</Label>
                    <Input
                      id="name"
                      name="name"
                      type="text"
                      placeholder="e.g., Jane Doe"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="focus:ring-ring focus:border-ring"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Your Email *</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="e.g., <EMAIL>"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="focus:ring-ring focus:border-ring"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subject">Subject</Label>
                  <Input
                    id="subject"
                    name="subject"
                    type="text"
                    placeholder="e.g., Video Editing Inquiry"
                    value={formData.subject}
                    onChange={handleInputChange}
                    className="focus:ring-ring focus:border-ring"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">Your Message *</Label>
                  <Textarea
                    id="message"
                    name="message"
                    placeholder="Tell me about your project..."
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={5}
                    className="focus:ring-ring focus:border-ring resize-none"
                  />
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  variant="secondary"
                  className="w-full py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 disabled:scale-100"
                >
                  {isSubmitting ? (
                    'Sending...'
                  ) : (
                    <>
                      Send Message
                      <Send size={18} className="ml-2" />
                    </>
                  )}
                </Button>

                {/* Status Messages */}
                {submitStatus === 'success' && (
                  <div className="text-success text-center font-medium">
                    Thank you! Your message has been sent successfully.
                  </div>
                )}
                {submitStatus === 'error' && (
                  <div className="text-destructive text-center font-medium">
                    Sorry, there was an error sending your message. Please try again.
                  </div>
                )}
              </form>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <div className="space-y-6 animate-on-scroll animate-slide-in-right stagger-4">
            <Card className="shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader>
                <CardTitle className="text-2xl font-heading text-primary">
                  Get in Touch
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-5">
                {contactInfo.map((info, index) => (
                  <div key={index} className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                      <info.icon size={20} className="text-primary" />
                    </div>
                    <div>
                      <div className="font-semibold text-primary">{info.label}</div>
                      {info.href !== '#' ? (
                        <a
                          href={info.href}
                          className="text-muted-foreground hover:text-secondary transition-colors"
                        >
                          {info.value}
                        </a>
                      ) : (
                        <div className="text-muted-foreground">{info.value}</div>
                      )}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Social Links */}
            <Card className="shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in stagger-5">
              <CardHeader>
                <CardTitle className="text-2xl font-heading text-primary">
                  Follow Me
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4">
                  {socialLinks.map(({ icon: Icon, href, label }) => (
                    <a
                      key={label}
                      href={href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-12 h-12 bg-primary/10 hover:bg-accent text-primary hover:text-accent-foreground rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                      aria-label={label}
                    >
                      <Icon size={20} />
                    </a>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* WhatsApp Quick Contact */}
            <Card className="shadow-lg bg-success/10 border-success/20 hover:shadow-xl transition-all duration-300 animate-glow animate-scale-in stagger-6">
              <CardContent className="p-6">
                <div className="text-center">
                  <h3 className="font-heading font-semibold text-success mb-2">
                    Quick Chat on WhatsApp
                  </h3>
                  <p className="text-success/80 text-sm mb-4">
                    Need immediate assistance? Let&apos;s chat!
                  </p>
                  <Button
                    asChild
                    className="bg-success hover:bg-success/90 text-success-foreground px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105"
                  >
                    <a
                      href="https://wa.me/9779840692118"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2"
                    >
                      <Phone size={18} />
                      Chat on WhatsApp
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
