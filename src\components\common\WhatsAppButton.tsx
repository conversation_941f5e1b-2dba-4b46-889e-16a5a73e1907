'use client';

import { useState, useEffect } from 'react';
import { FaWhatsapp } from "react-icons/fa";
import { Button } from '@/components/ui/button';

export default function WhatsAppButton() {
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  // Your WhatsApp number (replace with actual number)
  const whatsappNumber = '+977XXXXXXXXXX'; // Replace with your actual WhatsApp number
  const message = encodeURIComponent('Hi! I found your portfolio and would like to discuss a video editing project.');

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  useEffect(() => {
    // Show tooltip after 3 seconds of being visible
    if (isVisible) {
      const timer = setTimeout(() => {
        setShowTooltip(true);
        // Hide tooltip after 5 seconds
        setTimeout(() => setShowTooltip(false), 5000);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isVisible]);

  const handleWhatsAppClick = () => {
    const whatsappUrl = `https://wa.me/${whatsappNumber.replace(/[^0-9]/g, '')}?text=${message}`;
    window.open(whatsappUrl, '_blank', 'noopener,noreferrer');
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-24 right-8 z-50">
      {/* Auto-show Tooltip */}
      <div className={`
        absolute right-16 top-1/2 -translate-y-1/2 bg-success text-success-foreground px-4 py-3 rounded-lg text-sm whitespace-nowrap
        transition-all duration-500 pointer-events-none shadow-lg
        ${showTooltip && !isHovered ? 'opacity-100 translate-x-0 scale-100' : 'opacity-0 translate-x-4 scale-95'}
      `}>
        👋 Need help with video editing?
        <div className="absolute top-1/2 -translate-y-1/2 -right-1 w-2 h-2 bg-success rotate-45"></div>
      </div>

      <Button
        variant="ghost"
        onClick={handleWhatsAppClick}
        onMouseEnter={() => {
          setIsHovered(true);
          setShowTooltip(false);
        }}
        onMouseLeave={() => setIsHovered(false)}
        className={`group transition-all duration-300 p-0 h-auto w-auto hover:bg-transparent ${
          isVisible ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'
        }`}
        aria-label="Contact via WhatsApp"
      >
        {/* Main WhatsApp Button */}
        <div className="relative">
          {/* Ripple Effect Background */}
          <div className="absolute inset-0 rounded-full bg-success opacity-30 animate-ping"></div>
          <div className="absolute inset-0 rounded-full bg-success opacity-20 animate-ping animation-delay-1000"></div>

          <div className={`
            relative bg-success hover:bg-success/90 text-success-foreground rounded-full p-4 shadow-lg hover:shadow-xl
            transition-all duration-300 transform hover:scale-110 group-hover:rotate-12
            ${isHovered ? 'shadow-success/50' : ''}
          `}>
            <FaWhatsapp  size={30} className="transition-transform duration-300" />
          </div>

          {/* Notification Dot */}
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full animate-ping"></div>
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full"></div>
        </div>

        {/* Hover Tooltip */}
        <div className={`
          absolute right-16 top-1/2 -translate-y-1/2 bg-popover text-popover-foreground px-3 py-2 rounded-lg text-sm whitespace-nowrap
          transition-all duration-300 pointer-events-none shadow-lg border border-border
          ${isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2'}
        `}>
          Chat with us on WhatsApp
          <div className="absolute top-1/2 -translate-y-1/2 -right-1 w-2 h-2 bg-popover border-r border-b border-border rotate-45"></div>
        </div>
      </Button>
    </div>
  );
}
