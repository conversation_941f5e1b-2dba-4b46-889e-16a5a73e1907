'use client';

import { Dialog, DialogContent } from '@/components/ui/dialog';

interface VideoDialogProps {
  isOpen: boolean;
  onClose: () => void;
  videoId: string | null;
  videoTitle?: string;
  variant?: 'default' | 'compact';
}

export function VideoDialog({ 
  isOpen, 
  onClose, 
  videoId, 
  videoTitle = 'Video',
  variant = 'default' 
}: VideoDialogProps) {
  const getYouTubeEmbedUrl = (id: string) => {
    return `https://www.youtube.com/embed/${id}?autoplay=1&rel=0&modestbranding=1`;
  };

  const dialogStyles = variant === 'compact' 
    ? "max-w-4xl w-full p-0"
    : "w-[95vw] sm:max-w-2xl md:max-w-4xl lg:max-w-6xl p-0 bg-black border-0";

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className={dialogStyles}>
        <div className="relative w-full aspect-video">          
          {videoId && (
            <iframe
              src={getYouTubeEmbedUrl(videoId)}
              title={videoTitle}
              className={`w-full h-full ${variant === 'compact' ? 'rounded-lg' : ''}`}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
